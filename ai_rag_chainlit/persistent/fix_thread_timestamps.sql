-- 修复线程时间戳的SQL脚本
-- 为没有创建时间的线程添加时间戳

-- 1. 检查当前没有创建时间的线程数量
SELECT COUNT(*) as threads_without_timestamp 
FROM threads 
WHERE "createdAt" IS NULL OR "createdAt" = '';

-- 2. 为没有创建时间的线程设置创建时间
-- 使用该线程中最早的步骤时间作为线程创建时间
UPDATE threads 
SET "createdAt" = (
    SELECT MIN(s."createdAt") 
    FROM steps s 
    WHERE s."threadId" = threads."id" 
    AND s."createdAt" IS NOT NULL 
    AND s."createdAt" != ''
)
WHERE ("createdAt" IS NULL OR "createdAt" = '')
AND EXISTS (
    SELECT 1 
    FROM steps s 
    WHERE s."threadId" = threads."id" 
    AND s."createdAt" IS NOT NULL 
    AND s."createdAt" != ''
);

-- 3. 对于仍然没有时间戳的线程，使用当前时间
UPDATE threads 
SET "createdAt" = NOW()::text
WHERE "createdAt" IS NULL OR "createdAt" = '';

-- 4. 验证修复结果
SELECT COUNT(*) as threads_with_timestamp 
FROM threads 
WHERE "createdAt" IS NOT NULL AND "createdAt" != '';

-- 5. 显示最近的几个线程及其时间戳
SELECT 
    "id",
    "name",
    "createdAt",
    "userIdentifier"
FROM threads 
WHERE "createdAt" IS NOT NULL 
ORDER BY "createdAt" DESC 
LIMIT 10;
