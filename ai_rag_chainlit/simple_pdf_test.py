#!/usr/bin/env python3
"""
简化的PDF功能测试
只测试侧边栏显示功能
"""

import chainlit as cl
from typing import List
from chainlit.element import ElementBased


async def view_pdf(elements: List[ElementBased]):
    """查看PDF文件"""
    files = []
    contents = []
    for element in elements:
        if element.name.endswith(".pdf"):
            pdf = cl.Pdf(name=element.name, display="side", path=element.path, page=1)
            files.append(pdf)
            contents.append(element.name)
    if len(files) == 0:
        return
    await cl.Message(content=f"查看PDF文件：" + "，".join(contents), elements=files).send()


@cl.on_chat_start
async def start():
    """聊天开始"""
    await cl.Message(
        content="📄 简化PDF测试环境\n\n请上传PDF文件进行测试。"
    ).send()


@cl.on_message
async def main(message: cl.Message):
    """处理消息和文件上传"""
    
    # 收集PDF文件
    pdf_elements = []
    for element in message.elements:
        if hasattr(element, 'name') and element.name.endswith('.pdf'):
            pdf_elements.append(element)
    
    # 如果有PDF文件，显示PDF预览
    if pdf_elements:
        await view_pdf(pdf_elements)
    
    # 普通消息处理
    await cl.Message(
        content=f"收到消息: {message.content}"
    ).send()


if __name__ == "__main__":
    print("📄 简化PDF测试")
    print("运行命令: chainlit run simple_pdf_test.py -w")
