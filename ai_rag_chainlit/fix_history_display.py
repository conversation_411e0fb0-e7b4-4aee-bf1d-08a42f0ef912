#!/usr/bin/env python3
"""
修复历史记录显示问题的脚本
主要解决线程时间戳缺失的问题
"""

import os
import asyncio
from datetime import datetime
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text


async def fix_thread_timestamps():
    """修复线程时间戳"""
    
    # 获取数据库连接字符串
    connection_string = os.environ.get("CONNECTION_STRING")
    if not connection_string:
        print("❌ 错误：未找到 CONNECTION_STRING 环境变量")
        return
    
    print("🔧 开始修复线程时间戳...")
    
    # 创建异步数据库引擎
    engine = create_async_engine(connection_string)
    
    try:
        async with engine.begin() as conn:
            # 1. 检查当前没有创建时间的线程数量
            result = await conn.execute(text("""
                SELECT COUNT(*) as count 
                FROM threads 
                WHERE "createdAt" IS NULL OR "createdAt" = ''
            """))
            count_before = result.fetchone()[0]
            print(f"📊 发现 {count_before} 个没有时间戳的线程")
            
            if count_before == 0:
                print("✅ 所有线程都已有时间戳，无需修复")
                return
            
            # 2. 为没有创建时间的线程设置创建时间（使用最早的步骤时间）
            await conn.execute(text("""
                UPDATE threads 
                SET "createdAt" = (
                    SELECT MIN(s."createdAt") 
                    FROM steps s 
                    WHERE s."threadId" = threads."id" 
                    AND s."createdAt" IS NOT NULL 
                    AND s."createdAt" != ''
                )
                WHERE ("createdAt" IS NULL OR "createdAt" = '')
                AND EXISTS (
                    SELECT 1 
                    FROM steps s 
                    WHERE s."threadId" = threads."id" 
                    AND s."createdAt" IS NOT NULL 
                    AND s."createdAt" != ''
                )
            """))
            print("🔄 已使用步骤时间更新线程时间戳")
            
            # 3. 对于仍然没有时间戳的线程，使用当前时间
            current_time = datetime.now().isoformat() + "Z"
            await conn.execute(text("""
                UPDATE threads 
                SET "createdAt" = :current_time
                WHERE "createdAt" IS NULL OR "createdAt" = ''
            """), {"current_time": current_time})
            print("🕒 已为剩余线程设置当前时间")
            
            # 4. 验证修复结果
            result = await conn.execute(text("""
                SELECT COUNT(*) as count 
                FROM threads 
                WHERE "createdAt" IS NOT NULL AND "createdAt" != ''
            """))
            count_after = result.fetchone()[0]
            print(f"✅ 修复完成，现在有 {count_after} 个线程有时间戳")
            
            # 5. 显示最近的几个线程
            result = await conn.execute(text("""
                SELECT 
                    "id",
                    "name",
                    "createdAt",
                    "userIdentifier"
                FROM threads 
                WHERE "createdAt" IS NOT NULL 
                ORDER BY "createdAt" DESC 
                LIMIT 5
            """))
            
            print("\n📋 最近的线程记录：")
            for row in result.fetchall():
                thread_id = str(row[0])[:8] + "..."
                name = row[1] or "未命名对话"
                created_at = row[2]
                user = row[3] or "匿名用户"
                print(f"  • {name} ({thread_id}) - {created_at} - {user}")
                
    except Exception as e:
        print(f"❌ 修复过程中出现错误：{e}")
    finally:
        await engine.dispose()


async def check_data_layer_config():
    """检查数据层配置"""
    print("\n🔍 检查数据层配置...")
    
    # 检查环境变量
    connection_string = os.environ.get("CONNECTION_STRING")
    if connection_string:
        print("✅ CONNECTION_STRING 环境变量已设置")
    else:
        print("❌ CONNECTION_STRING 环境变量未设置")
        return False
    
    # 检查数据库连接
    try:
        engine = create_async_engine(connection_string)
        async with engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            print("✅ 数据库连接正常")
        await engine.dispose()
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败：{e}")
        return False


async def main():
    """主函数"""
    print("🚀 历史记录显示修复工具")
    print("=" * 50)
    
    # 检查配置
    if not await check_data_layer_config():
        print("\n❌ 配置检查失败，请检查数据库配置")
        return
    
    # 修复时间戳
    await fix_thread_timestamps()
    
    print("\n" + "=" * 50)
    print("🎉 修复完成！")
    print("\n💡 建议：")
    print("1. 重启 Chainlit 应用")
    print("2. 刷新浏览器页面")
    print("3. 检查左侧历史记录是否正常显示时间")


if __name__ == "__main__":
    asyncio.run(main())
