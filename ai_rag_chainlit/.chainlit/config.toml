# ================================
# Chainlit 项目基础配置
# ================================
[project]
# 是否启用遥测数据收集（默认：true）。不会收集个人数据，仅用于产品改进
# Whether to enable telemetry (default: true). No personal data is collected.
enable_telemetry = true

# 每个用户使用应用时需要提供的环境变量列表（如API密钥等敏感信息）
# List of environment variables to be provided by each user to use the app.
user_env = []

# 连接丢失时会话保存的持续时间（秒）- 3600秒 = 1小时
# Duration (in seconds) during which the session is saved when the connection is lost
session_timeout = 3600

# 启用第三方缓存（例如 LangChain 缓存）- 可提高响应速度但占用内存
# Enable third parties caching (e.g LangChain cache)
cache = false

# 授权的跨域来源 - "*" 表示允许所有域名访问（生产环境建议限制具体域名）
# Authorized origins
allow_origins = ["*"]

# 是否跟随符号链接进行资源挂载（参见 GitHub issue #317）
# Follow symlink for asset mount (see https://github.com/Chainlit/chainlit/issues/317)
# follow_symlink = false

# ================================
# 功能特性配置
# ================================
[features]
# 处理并显示消息中的HTML内容 - 存在安全风险，可能导致XSS攻击，建议保持关闭
# Process and display HTML in messages. This can be a security risk (see https://stackoverflow.com/questions/19603097/why-is-it-dangerous-to-render-user-generated-html-or-javascript)
unsafe_allow_html = false

# 处理并显示LaTeX数学表达式 - 适用于科学计算场景，但可能与消息中的"$"字符冲突
# Process and display mathematical expressions. This can clash with "$" characters in messages.
latex = false

# 自动为对话线程标记当前聊天配置文件 - 便于对话分类和管理
# Automatically tag threads with the current chat profile (if a chat profile is used)
auto_tag_thread = true

# 允许用户编辑自己已发送的消息 - 提升用户体验
# Allow users to edit their own messages
edit_message = true

# ================================
# 文件上传功能配置
# ================================
# 授权用户在聊天中随时上传文件
# Authorize users to spontaneously upload files with messages
[features.spontaneous_file_upload]
    enabled = true              # 启用文件上传功能
    accept = ["*/*"]           # 接受的文件类型 - "*/*" 表示所有类型（可限制为特定格式如 ["image/*", "text/*"]）
    max_files = 20             # 单次最多上传文件数量
    max_size_mb = 500          # 单个文件最大大小（MB）

# ================================
# 音频录制功能配置
# ================================
[features.audio]
    # 音频录制的音量阈值（分贝）- 低于此值视为静音
    # Threshold for audio recording
    min_decibels = -45

    # 用户开始说话前的等待时间（毫秒）- 录制开始后等待用户说话的时间
    # Delay for the user to start speaking in MS
    initial_silence_timeout = 3000

    # 用户停止说话后的等待时间（毫秒）- 检测到静音后自动停止录制的时间
    # Delay for the user to continue speaking in MS. If the user stops speaking for this duration, the recording will stop.
    silence_timeout = 1500

    # 录制的最大时长（毫秒）- 超过此时间将强制停止录制
    # Above this duration (MS), the recording will forcefully stop.
    max_duration = 15000

    # 音频数据块的持续时间（毫秒）- 音频处理的时间间隔
    # Duration of the audio chunks in MS
    chunk_duration = 1000

    # 音频采样率 - 44100Hz为CD音质，数值越高音质越好但文件越大
    # Sample rate of the audio
    sample_rate = 44100

# ================================
# 用户界面(UI)配置
# ================================
[UI]
# AI助手的显示名称 - 在界面顶部和对话中显示
# Name of the assistant.
name = "Assistant"

# 助手的描述信息 - 用于HTML meta标签，有助于SEO优化
# Description of the assistant. This is used for HTML tags.
# description = ""

# 大尺寸内容默认折叠显示 - 保持界面整洁，用户可点击展开
# Large size content are by default collapsed for a cleaner ui
default_collapse_content = true

# 思维链(CoT)显示模式 - "hidden"隐藏/"tool_call"仅显示工具调用/"full"完整显示推理过程
# Chain of Thought (CoT) display mode. Can be "hidden", "tool_call" or "full".
cot = "full"

# GitHub仓库链接 - 在界面头部添加GitHub按钮，方便用户访问源码
# Link to your github repo. This will add a github button in the UI's header.
# github = ""

# 自定义CSS样式文件 - 可从public目录提供或使用外部链接来自定义界面外观
# Specify a CSS file that can be used to customize the user interface.
# The CSS file can be served from the public directory or via an external link.
custom_css = "/public/custom.css"

# 自定义JavaScript文件 - 可从public目录提供，用于添加自定义交互功能
# Specify a Javascript file that can be used to customize the user interface.
# The Javascript file can be served from the public directory.
# custom_js = "/public/test.js"

# 自定义字体URL - 指定Google Fonts或其他字体服务的链接
# Specify a custom font url.
# custom_font = "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap"

# 自定义meta图片URL - 用于社交媒体分享时的预览图片
# Specify a custom meta image url.
# custom_meta_image_url = "https://chainlit-cloud.s3.eu-west-3.amazonaws.com/logo/chainlit_banner.png"

# 自定义前端构建目录 - 用于自定义前端代码，注意相对路径不应以斜杠开头
# Specify a custom build directory for the frontend.
# This can be used to customize the frontend code.
# Be careful: If this is a relative path, it should not start with a slash.
# custom_build = "./public/build"

# ================================
# 主题配置
# ================================
[UI.theme]
    default = "dark"                    # 默认主题模式 - "light"浅色/"dark"深色
    #layout = "wide"                    # 布局模式 - "wide"宽屏布局/"normal"标准布局
    #font_family = "Inter, sans-serif"  # 全局字体设置

# ================================
# 浅色主题自定义配置
# ================================
# 覆盖默认的MUI浅色主题设置 (详见 theme.ts)
# Override default MUI light theme. (Check theme.ts)
[UI.theme.light]
    #background = "#FAFAFA"             # 页面背景色
    #paper = "#FFFFFF"                  # 卡片/面板背景色

    # 主色调配置
    [UI.theme.light.primary]
        #main = "#F80061"               # 主色 - 按钮、链接等主要元素颜色
        #dark = "#980039"               # 深色变体 - 悬停状态等
        #light = "#FFE7EB"              # 浅色变体 - 背景高亮等

    # 文字颜色配置
    [UI.theme.light.text]
        #primary = "#212121"            # 主要文字颜色
        #secondary = "#616161"          # 次要文字颜色

# ================================
# 深色主题自定义配置
# ================================
# 覆盖默认的MUI深色主题设置 (详见 theme.ts)
# Override default MUI dark theme. (Check theme.ts)
[UI.theme.dark]
    #background = "#FAFAFA"             # 页面背景色（深色主题通常使用深色背景）
    #paper = "#FFFFFF"                  # 卡片/面板背景色

    # 主色调配置
    [UI.theme.dark.primary]
        #main = "#F80061"               # 主色
        #dark = "#980039"               # 深色变体
        #light = "#FFE7EB"              # 浅色变体

    # 文字颜色配置
    [UI.theme.dark.text]
        #primary = "#EEEEEE"            # 主要文字颜色（深色主题使用浅色文字）
        #secondary = "#BDBDBD"          # 次要文字颜色

# ================================
# 元数据信息
# ================================
[meta]
generated_by = "1.3.2"                 # 生成此配置文件的Chainlit版本号
