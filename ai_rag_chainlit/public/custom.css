/* 自定义CSS样式用于美化Chainlit界面 */

/* 导入Google字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* 设置全局字体 */
* {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* 全局样式重置 */
body {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  margin: 0;
  padding: 0;
}

/* 调整字体大小和行高 */
.message-content {
  font-size: 15px !important;
  line-height: 1.7 !important;
  font-weight: 400 !important;
}

.user-message .message-content {
  font-size: 15px !important;
  font-weight: 500 !important;
}

/* 侧边栏样式优化 */
.sidebar, [data-testid="sidebar"] {
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%) !important;
  border-right: 1px solid #e2e8f0 !important;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.08) !important;
  backdrop-filter: blur(10px) !important;
  width: 320px !important;
}

.sidebar-header, [data-testid="sidebar-header"] {
  padding: 24px 20px !important;
  border-bottom: 1px solid #e2e8f0 !important;
  background: rgba(255, 255, 255, 0.9) !important;
}

/* 聊天历史记录标题 */
.sidebar h3, .sidebar-header h3 {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #1e293b !important;
  margin: 0 0 16px 0 !important;
}

/* 聊天线程列表样式 */
.thread-list-item, [data-testid="thread-history-item"] {
  border-radius: 12px !important;
  margin: 6px 12px !important;
  padding: 16px 18px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: rgba(255, 255, 255, 0.7) !important;
  border: 1px solid transparent !important;
  cursor: pointer !important;
  position: relative !important;
}

/* 确保线程标题显示 */
.thread-list-item .thread-title,
[data-testid="thread-history-item"] .thread-title,
.thread-list-item h4,
[data-testid="thread-history-item"] h4 {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #1e293b !important;
  margin: 0 0 8px 0 !important;
  line-height: 1.4 !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 确保时间戳显示 */
.thread-list-item .thread-timestamp,
[data-testid="thread-history-item"] .thread-timestamp,
.thread-list-item .timestamp,
[data-testid="thread-history-item"] .timestamp,
.thread-list-item time,
[data-testid="thread-history-item"] time,
.thread-list-item .created-at,
[data-testid="thread-history-item"] .created-at {
  font-size: 12px !important;
  color: #64748b !important;
  margin: 4px 0 0 0 !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  font-weight: 400 !important;
}

/* 线程预览文本 */
.thread-list-item .thread-preview,
[data-testid="thread-history-item"] .thread-preview {
  font-size: 13px !important;
  color: #64748b !important;
  margin: 6px 0 0 0 !important;
  line-height: 1.3 !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.thread-list-item:hover, [data-testid="thread-history-item"]:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
  border-color: #cbd5e1 !important;
}

.thread-list-item:hover .thread-timestamp,
[data-testid="thread-history-item"]:hover .thread-timestamp,
.thread-list-item:hover .timestamp,
[data-testid="thread-history-item"]:hover .timestamp {
  color: #475569 !important;
}

.thread-list-item.active, [data-testid="thread-history-item"][data-active="true"] {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3) !important;
  border-color: #3b82f6 !important;
}

.thread-list-item.active .thread-title,
[data-testid="thread-history-item"][data-active="true"] .thread-title,
.thread-list-item.active h4,
[data-testid="thread-history-item"][data-active="true"] h4 {
  color: white !important;
}

.thread-list-item.active .thread-timestamp,
[data-testid="thread-history-item"][data-active="true"] .thread-timestamp,
.thread-list-item.active .timestamp,
[data-testid="thread-history-item"][data-active="true"] .timestamp,
.thread-list-item.active .thread-preview,
[data-testid="thread-history-item"][data-active="true"] .thread-preview {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* 头部样式优化 */
.header, [data-testid="header"] {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
  border-bottom: 1px solid #e2e8f0 !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  padding: 20px 32px !important;
  backdrop-filter: blur(10px) !important;
}

.header h1, .header-title {
  font-size: 24px !important;
  font-weight: 700 !important;
  color: #1e293b !important;
  margin: 0 !important;
}

/* 消息气泡样式优化 */
.user-message, [data-author="user"] {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  border-radius: 24px 24px 8px 24px !important;
  padding: 18px 24px !important;
  color: white !important;
  margin: 16px 0 !important;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.25) !important;
  max-width: 80% !important;
  margin-left: auto !important;
}

.assistant-message, [data-author="assistant"] {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
  border-radius: 24px 24px 24px 8px !important;
  padding: 18px 24px !important;
  color: #1e293b !important;
  margin: 16px 0 !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08) !important;
  max-width: 80% !important;
}

/* 输入框样式优化 */
.chat-input, [data-testid="chat-input"] {
  border-radius: 24px !important;
  border: 2px solid #e2e8f0 !important;
  padding: 16px 24px !important;
  background: rgba(255, 255, 255, 0.9) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  font-size: 15px !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.chat-input:focus, [data-testid="chat-input"]:focus {
  outline: none !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15), 0 8px 32px rgba(0, 0, 0, 0.12) !important;
  background: white !important;
}

/* 按钮样式优化 */
button {
  border-radius: 16px !important;
  padding: 12px 24px !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border: none !important;
}

button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

/* 发送按钮特殊样式 */
button[type="submit"], .send-button {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  color: white !important;
}

button[type="submit"]:hover, .send-button:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%) !important;
}

/* 增强动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.message {
  animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.thread-list-item {
  animation: slideInLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 加载动画 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

/* 响应式设计优化 */
@media (max-width: 1024px) {
  .sidebar {
    width: 280px !important;
  }
}

@media (max-width: 768px) {
  .message-content {
    font-size: 14px !important;
  }

  .header {
    padding: 16px 20px !important;
  }

  .sidebar {
    width: 260px !important;
  }

  .user-message, .assistant-message {
    max-width: 90% !important;
    padding: 14px 18px !important;
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: 100% !important;
    position: fixed !important;
    z-index: 1000 !important;
  }
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.8);
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 8px;
  border: 2px solid rgba(241, 245, 249, 0.8);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
}

/* 文件上传区域样式 */
.file-upload-area {
  border: 2px dashed #cbd5e1 !important;
  border-radius: 16px !important;
  padding: 32px !important;
  text-align: center !important;
  background: rgba(248, 250, 252, 0.8) !important;
  transition: all 0.3s ease !important;
}

.file-upload-area:hover {
  border-color: #3b82f6 !important;
  background: rgba(59, 130, 246, 0.05) !important;
}

/* 状态指示器 */
.status-indicator {
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
  background: #10b981 !important;
  animation: pulse 2s infinite !important;
}

/* 工具提示样式 */
.tooltip {
  background: rgba(30, 41, 59, 0.9) !important;
  color: white !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  font-size: 12px !important;
  backdrop-filter: blur(10px) !important;
}

/* 历史记录分组标题样式 */
.thread-group-header,
.history-group-header,
[data-testid="thread-group-header"] {
  font-size: 13px !important;
  font-weight: 600 !important;
  color: #64748b !important;
  padding: 16px 20px 8px 20px !important;
  margin: 0 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 确保所有历史记录相关元素都显示 */
.thread-history,
.chat-history,
[data-testid="thread-history"],
[data-testid="chat-history"] {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.thread-history *,
.chat-history *,
[data-testid="thread-history"] *,
[data-testid="chat-history"] * {
  display: inherit !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 强制显示所有时间相关的元素 */
*[class*="time"],
*[class*="date"],
*[class*="created"],
*[class*="timestamp"] {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  font-size: 12px !important;
  color: #64748b !important;
}

/* 修复可能被隐藏的元素 */
.hidden,
[style*="display: none"],
[style*="visibility: hidden"],
[style*="opacity: 0"] {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}