import os
import sys
from typing import Optional, List

# 添加当前目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

import chainlit as cl
import chainlit.data as cl_data
from chainlit.element import ElementBased
from llama_index.core import Settings,VectorStoreIndex,SimpleDirectoryReader

from llama_index.core.chat_engine import SimpleChatEngine
from llama_index.core.chat_engine.types import ChatMode

from llms import gitee_deepseek_v3_llm
from embeddings import embed_model_local_bge_small
from persistent.minio_storage_client import MinioStorageClient
from persistent.postgresql_data_layer import PostgreSQLDataLayer


Settings.llm = gitee_deepseek_v3_llm()
Settings.embed_model = embed_model_local_bge_small()

# 实现聊天数据持久化
storage_client = MinioStorageClient()
cl_data._data_layer = PostgreSQLDataLayer(conninfo=os.environ["CONNECTION_STRING"], storage_provider=storage_client)

Settings.embed_model = embed_model_local_bge_small()
Settings.llm = gitee_deepseek_v3_llm()

async def view_pdf(elements: List[ElementBased]):
    """查看PDF文件"""
    files = []
    contents = []
    for element in elements:
        if element.name.endswith(".pdf"):
            pdf = cl.Pdf(name=element.name, display="side", path=element.path, page=1)
            files.append(pdf)
            contents.append(element.name)
    if len(files) == 0:
        return
    await cl.Message(content=f"查看PDF文件：" + "，".join(contents), elements=files).send()

@cl.on_chat_start
async def start():

    # 直接与大模型对话引擎
    chat_engine = SimpleChatEngine.from_defaults()
    cl.user_session.set("chat_engine", chat_engine)

    await cl.Message(
        author="Assistant", content="您好，我是印刷智能制造实验室小助手！有什么问题请问我！"
    ).send()


@cl.on_message
async def main(message: cl.Message):
    chat_engine = cl.user_session.get("chat_engine")
    msg = cl.Message(content="", author="Assistant")

    files = []
    pdf_elements = []

    # 获取用户上传的文件
    for element in message.elements:
        if isinstance(element, cl.File) or isinstance(element, cl.Image):
            files.append(element.path)
            # 收集PDF文件用于预览
            if element.name.endswith(".pdf"):
                pdf_elements.append(element)

    # 如果有PDF文件，显示PDF预览
    if pdf_elements:
        await view_pdf(pdf_elements)

    # 文件索引处理
    if len(files) > 0:
        # 加载数据（数据连接器）
        data = SimpleDirectoryReader(input_files=files).load_data()
        # 构建索引，正常执行一次即可
        index = VectorStoreIndex.from_documents(data, show_progress=True)
        chat_engine = index.as_chat_engine(chat_mode=ChatMode.CONTEXT)
        cl.user_session.set("chat_engine", chat_engine)

    res = await cl.make_async(chat_engine.stream_chat)(message.content)

    # 流式界面输出
    for token in res.response_gen:
        await msg.stream_token(token)
    await msg.send()

@cl.password_auth_callback
def auth_callback(username: str, password: str) -> Optional[cl.User]:
    # 可以对接第三方认证
    if (username, password) == ("admin", "admin"):
        return cl.User(identifier="admin",
                       metadata={"role": "admin", "provider": "credentials"})
    else:
        return None